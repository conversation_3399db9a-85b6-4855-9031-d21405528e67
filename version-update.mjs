import * as fs from 'fs/promises';
import * as path from 'path';

function getKSTISOString() {
	const date = new Date();
	const offset = 9 * 60; // KST is UTC+9

	return new Date(date.getTime() + offset * 60 * 1000).toISOString().split('.')[0] + '+09:00';
}

// 업데이트할 버전을 설정합니다.
const newVersion = '0.9.1193';

// 변경 사항을 설정합니다.
const changeNotes = '입고 목록 점검/수리 대기중, 점검/수리 완료 카운터 추가';

// 현재 날짜를 가져옵니다.
const formattedDate = getKSTISOString();

// 파일들의 경로를 설정합니다.
const files = [
	path.join(process.cwd(), 'src-tauri', 'tauri.conf.json'),
	path.join(process.cwd(), 'src-tauri', 'Cargo.toml'),
	path.join(process.cwd(), 'latest.json'),
	path.join(process.cwd(), 'src', 'routes', 'login', '+page.svelte')
];

files.forEach(async (file) => {
	try {
		const data = await fs.readFile(file, 'utf8');
		let updatedContent;

		// JSON 파일 업데이트
		if (path.extname(file) === '.json') {
			const json = JSON.parse(data);

			json.version = newVersion; // latest.json

			if (json.notes) {
				json.notes = changeNotes; // 변경 사항을 notes 필드에 추가
			}

			if (json.url) {
				json.url = json.url.replace(/_\d+\.\d+\.\d+_/, `_${newVersion}_`);
			}

			if (json.pub_date) {
				json.pub_date = formattedDate;
			}

			updatedContent = JSON.stringify(json, null, 2);
		}

		// TOML 파일 업데이트
		if (path.extname(file) === '.toml') {
			updatedContent = data.replace(/^version\s*=\s*".*"/m, `version = "${newVersion}"`);
		}

		// Svelte 파일 업데이트
		if (path.extname(file) === '.svelte') {
			updatedContent = data.replace(
				/Ver \d+\.\d+\.\d+ \(\d{4}-\d{2}-\d{2}\)/,
				`Ver ${newVersion} (${formattedDate.slice(0, 10)})`
			);
		}

		// 변경된 내용을 파일에 저장합니다.
		await fs.writeFile(file, updatedContent, 'utf8');
		console.log(`버전이 업데이트되었습니다: ${file}`);
	} catch (err) {
		console.error(`파일을 처리하는 데 실패했습니다: ${file}`, err);
	}
});
