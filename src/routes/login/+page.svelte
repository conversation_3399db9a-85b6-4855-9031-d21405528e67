<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';

	import { authClient } from '$lib/AxiosBackend';
	import { isUser, setUser } from '$lib/User';
	import { executeMessage } from '$lib/Functions';

	import { setTheme, themes } from '$lib/Themes';

	import Icon from 'svelte-awesome';
	import { faKey } from '@fortawesome/free-solid-svg-icons/faKey';
	import { faPalette } from '@fortawesome/free-solid-svg-icons/faPalette';

	if (isUser()) {
		goto('/dashboard');
	}

	// 클라이언트 측에서 로그인 처리
	let username: string = $state('');
	let password: string = $state('');

	async function handleLogin(event: SubmitEvent) {
		event.preventDefault();

		const payload = {
			username,
			password
		};

		const loginEndpoint = `/wms/login`;
		const userEndpoint = `/wms/user`;
		try {
			const loginResponse = await authClient.post(loginEndpoint, payload);

			if (loginResponse) {
				await authClient.get('/sanctum/csrf-cookie');

				try {
					const { status, data } = await authClient.get(userEndpoint);

					if (status === 200 && data.success) {
						const user = data.data.user;
						await setUser(user);
						
						// 로그인 성공 후 잠시 대기하여 로그가 기록될 시간 확보
						await new Promise(resolve => setTimeout(resolve, 500));

						await goto('/dashboard');
					} else {
						await executeMessage('회원 정보를 받아올 수 없습니다.', 'warning');
					}
				} catch (userError) {
					await executeMessage(`사용자 정보 오류: ${userError}`, 'error');
				}
			}
		} catch (err) {
			password = '';
			await executeMessage(`잘못된 아이디 또는 비밀번호 입니다: ${err}`, 'error');
		}
	}

	const defaultTheme = 'light';
	let currentTheme: string = $state(defaultTheme);
	onMount(() => {
		if (typeof window !== 'undefined') {
			const theme = window.localStorage.getItem('theme');
			if (theme && themes.includes(theme)) {
				document.documentElement.setAttribute('data-theme', theme);
				setTheme(theme);
				currentTheme = theme;
			} else {
				setTheme(defaultTheme);
				currentTheme = defaultTheme;
			}
		}
	});
</script>

<!-- component -->
<div>
	<div class="relative flex flex-col justify-center h-screen overflow-hidden">
		<div
			class="w-full p-6 m-auto bg-base-100 text-base-content rounded-md shadow-md ring-2 ring-base-content/20 lg:max-w-lg"
		>
			<div class="flex justify-center">
				<div class="text-3xl font-semibold text-center">CornerStone Project WMS</div>
				<div class="dropdown dropdown-hover dropdown-end">
					<button class="btn btn-ghost" tabindex="0">
						<Icon data={faPalette} />
					</button>

					<ul
						class="dropdown-content menu z-[1] p-2 bg-base-100 shadow rounded-box w-96 h-96 border border-base-300"
					>
						{#each themes as theme}
							<li>
								<button
									class="justify-between"
									onclick={() => {
										setTheme(theme);
										currentTheme = theme;
									}}
								>
									{theme}
									{#if theme === currentTheme}
										<span class="badge badge-primary badge-xs">현재</span>
									{/if}
								</button>
							</li>
						{/each}
					</ul>
				</div>
			</div>

			<form onsubmit={handleLogin}>
				<div>
					<label class="label" for="username">
						<span class="label-text">아이디</span>
					</label>
					<input
						bind:value={username}
						class="w-full input input-bordered"
						id="username"
						name="username"
						required
						type="text"
					/>
				</div>
				<div>
					<label class="label" for="password">
						<span class="label-text">비밀번호</span>
					</label>
					<input
						bind:value={password}
						class="w-full input input-bordered"
						id="password"
						name="password"
						required
						type="password"
					/>
				</div>

				<div class="divider"></div>

				<div>
					<button class="btn btn-block">
						<Icon class="mr-2" data={faKey} />
						로그인
					</button>
				</div>
			</form>
		</div>

		<div class="w-full p-6 text-center">Ver 0.9.1194 (2025-06-23)</div>
	</div>
</div>
